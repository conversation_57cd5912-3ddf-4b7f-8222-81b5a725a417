﻿  *** Found installed NintendoSDK version 19.3.5.  That version is greater than the maximum suggested version of 16.2.6 and is unverified. ***
  Building RugbyEditor and ShaderCompileWorker...
  Using Visual Studio 2019 14.29.30159 toolchain (C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133) and Windows 10.0.22000.0 SDK (C:\Program Files (x86)\Windows Kits\10).
  [Upgrade]
  [Upgrade] Using backward-compatible build settings. The latest version of UE4 sets the following values by default, which may require code changes:
  [Upgrade]     bLegacyPublicIncludePaths = false                 => Omits subfolders from public include paths to reduce compiler command line length. (Previously: true).
  [Upgrade]     ShadowVariableWarningLevel = WarningLevel.Error   => Treats shadowed variable warnings as errors. (Previously: WarningLevel.Warning).
  [Upgrade]     PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs   => Set in build.cs files to enables IWYU-style PCH model. See https://docs.unrealengine.com/en-US/Programming/BuildTools/UnrealBuildTool/IWYU/index.html. (Previously: PCHUsageMode.UseSharedPCHs).
  [Upgrade] Suppress this message by setting 'DefaultBuildSettings = BuildSettingsVersion.V2;' in RugbyEditor.Target.cs, and explicitly overriding settings that differ from the new defaults.
  [Upgrade]
  Building 28 actions with 24 processes...
    [1/28] Module.Rugby.10_of_50.cpp
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(747): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(851): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(876): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(891): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(913): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(1095): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(1112): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/XDS/XDSXMLParser.cpp(1132): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:/NRL/Source/Rugby/Match/AI/Actions/RUActionFend.cpp(396): warning C4305: 'initializing': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [2/28] Module.Rugby.18_of_50.cpp
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/HeadCaptureActor.cpp(138): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HeadCaptureActor.cpp(272): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/Marking/RULineoutIndicator.cpp(38): warning C4305: 'argument': truncation from 'double' to 'float'
    [3/28] Module.Rugby.28_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/RugbyUnion/RUGameSettings.cpp(124): warning C4355: 'this': used in base member initializer list
    [4/28] Module.Rugby.12_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:\NRL\Source\Rugby\Match\AI\Roles\Competitors\RURoleStandardBallHolder.cpp(358): warning C4701: potentially uninitialized local variable 'kickType' used
    [5/28] Module.Rugby.11_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [6/28] Module.Rugby.4_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [7/28] Module.Rugby.32_of_50.cpp
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [8/28] Module.Rugby.16_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/Cutscenes/SSCutSceneManager.cpp(1958): warning C4996: 'UMovieSceneSequencePlayer::JumpToSeconds': JumpToSeconds is deprecated, use SetPlaybackPosition. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [9/28] Module.Rugby.14_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/AI/SetPlays/SSSetPlayManager.cpp(279): warning C4305: 'argument': truncation from 'double' to 'float'
W:/NRL/Source/Rugby/Match/AI/SetPlays/SSSetPlayManager.cpp(297): warning C4305: 'argument': truncation from 'double' to 'float'
    [10/28] Module.Rugby.13_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [11/28] Module.Rugby.31_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:\NRL\Plugins\wwStadiumTool\Source\wwStadiumRuntime\Public\SkyDomeBase.h(466): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUTeamFacesGenerator.cpp(1041): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUTeamFacesGenerator.cpp(1171): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [12/28] Module.Rugby.3_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Character/RugbyCharacter.cpp(2129): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Character/RugbyCharacter.cpp(2262): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:\Engine\Engine\Source\Runtime\Core\Public\Templates/Less.h(19): warning C4996: 'FName::operator <': Please use FastLess() / FNameFastLess or LexicalLess() / FNameLexicalLess instead. Default lexical sort order is deprecated to avoid unintended expensive sorting.  Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    W:\Engine\Engine\Source\Runtime\Core\Public\Templates/Less.h(18): note: while compiling class template member function 'bool TLess<T>::operator ()(const T &,const T &) const'
            with
            [
                T=FName
            ]
    W:\Engine\Engine\Source\Runtime\Core\Public\Templates/Sorting.h(23): note: see reference to function template instantiation 'bool TLess<T>::operator ()(const T &,const T &) const' being compiled
            with
            [
                T=FName
            ]
    W:\Engine\Engine\Source\Runtime\Core\Public\Templates/Sorting.h(108): note: see reference to class template instantiation 'TLess<T>' being compiled
            with
            [
                T=FName
            ]
    W:\Engine\Engine\Source\Runtime\Core\Public\Containers/Array.h(2520): note: see reference to function template instantiation 'void Sort<FName>(T *,const int32)' being compiled
            with
            [
                T=FName
            ]
    W:\Engine\Engine\Source\Runtime\Core\Public\Containers/Array.h(2519): note: while compiling class template member function 'void TArray<FName,FDefaultAllocator>::Sort(void)'
    W:/NRL/Source/Rugby/Character/RugbyCharacterAnimInstance.cpp(5196): note: see reference to function template instantiation 'void TArray<FName,FDefaultAllocator>::Sort(void)' being compiled
    W:\Engine\Engine\Source\Runtime\Core\Public\Misc/ITransaction.h(133): note: see reference to class template instantiation 'TArray<FName,FDefaultAllocator>' being compiled
    [13/28] Module.Rugby.33_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
    [14/28] Module.Rugby.30_of_50.cpp
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(722): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(735): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(749): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(762): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(776): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(789): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(812): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(824): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(836): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(859): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(871): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/RugbyUnion/RUStadiumManager.cpp(883): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [15/28] Module.Rugby.35_of_50.cpp
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
    [16/28] Module.Rugby.29_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [17/28] Module.Rugby.7_of_50.cpp
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Editor/PropertyEditor/Public/DetailWidgetRow.h(98): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Editor/PropertyEditor/Public/DetailWidgetRow.h(99): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Editor/PropertyEditor/Public/DetailWidgetRow.h(100): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/GameModes/RugbyGameModeBase.cpp(357): warning C4996: 'APlayerState::UniqueId': This member will be made private. Use GetUniqueId or SetUniqueId instead. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/GameModes/RugbyGameModeBase.cpp(359): warning C4996: 'APlayerState::UniqueId': This member will be made private. Use GetUniqueId or SetUniqueId instead. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/GameModes/RugbyGameModeBase.cpp(616): warning C4996: 'APlayerState::UniqueId': This member will be made private. Use GetUniqueId or SetUniqueId instead. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/GameModes/RugbyGameModeBase.cpp(1037): warning C4996: 'APlayerState::PlayerId': This member will be made private. Use GetPlayerId or SetPlayerId instead. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/GameModes/RugbyGameSession.cpp(74): warning C4996: 'APlayerState::PlayerId': This member will be made private. Use GetPlayerId or SetPlayerId instead. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/GameModes/RugbyGameSession.cpp(891): warning C4458: declaration of 'SessionName' hides class member
    W:\Engine\Engine\Source\Runtime\Engine\Classes\GameFramework/GameSession.h(53): note: see declaration of 'AGameSession::SessionName'
W:/NRL/Source/Rugby/GameModes/RugbyGameSession.cpp(918): warning C4458: declaration of 'SessionName' hides class member
    W:\Engine\Engine\Source\Runtime\Engine\Classes\GameFramework/GameSession.h(53): note: see declaration of 'AGameSession::SessionName'
W:/NRL/Source/Rugby/GameModes/RugbyGameSession.cpp(1524): warning C4458: declaration of 'SessionName' hides class member
    W:\Engine\Engine\Source\Runtime\Engine\Classes\GameFramework/GameSession.h(53): note: see declaration of 'AGameSession::SessionName'
W:/NRL/Source/Rugby/GameModes/RugbyGameState.cpp(473): warning C4996: 'APlayerState::PlayerId': This member will be made private. Use GetPlayerId or SetPlayerId instead. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/GameModes/RugbyGameViewportClient.cpp(37): warning C4458: declaration of 'GameInstance' hides class member
    W:\Engine\Engine\Source\Runtime\Engine\Classes\Engine/GameViewportClient.h(105): note: see declaration of 'UGameViewportClient::GameInstance'
W:/NRL/Source/Rugby/GameModes/RugbyGameViewportClient.cpp(168): warning C4458: declaration of 'Viewport' hides class member
    W:\Engine\Engine\Source\Runtime\Engine\Classes\Engine/GameViewportClient.h(637): note: see declaration of 'UGameViewportClient::Viewport'
W:/NRL/Source/Rugby/Mab/Central/Streamers/MabStreamerXML.cpp(339): warning C4996: 'strcat': This function or variable may be unsafe. Consider using strcat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/Central/Streamers/MabStreamerXML.cpp(334): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/Central/Streamers/MabStreamerXML.cpp(357): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/Central/Streamers/MabStreamerXML.cpp(372): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/Central/Streamers/MabStreamerXML.cpp(393): warning C4996: 'strcat': This function or variable may be unsafe. Consider using strcat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/Central/Streamers/MabStreamerXML.cpp(388): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/Central/Streamers/MabStreamerXML.cpp(677): warning C4996: 'strcat': This function or variable may be unsafe. Consider using strcat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/Central/Streamers/MabStreamerXML.cpp(740): warning C4996: 'strcat': This function or variable may be unsafe. Consider using strcat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/Central/Streamers/MabStreamerXML.cpp(757): warning C4996: 'strcat': This function or variable may be unsafe. Consider using strcat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/Central/Streamers/MabStreamerXML.cpp(624): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/Central/Streamers/MabStreamerXML.cpp(672): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/Central/Streamers/MabStreamerXML.cpp(737): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/Mab/Central/Streamers/MabStreamerXML.cpp(755): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:\Engine\Engine\Source\Runtime\Core\Public\Templates/Less.h(19): warning C4996: 'FName::operator <': Please use FastLess() / FNameFastLess or LexicalLess() / FNameLexicalLess instead. Default lexical sort order is deprecated to avoid unintended expensive sorting.  Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    W:\Engine\Engine\Source\Runtime\Core\Public\Templates/Less.h(18): note: while compiling class template member function 'bool TLess<T>::operator ()(const T &,const T &) const'
            with
            [
                T=FName
            ]
    W:\Engine\Engine\Source\Runtime\Core\Public\Templates/Sorting.h(23): note: see reference to function template instantiation 'bool TLess<T>::operator ()(const T &,const T &) const' being compiled
            with
            [
                T=FName
            ]
    W:\Engine\Engine\Source\Runtime\Core\Public\Templates/Sorting.h(108): note: see reference to class template instantiation 'TLess<T>' being compiled
            with
            [
                T=FName
            ]
    W:\Engine\Engine\Source\Runtime\Core\Public\Containers/Array.h(2520): note: see reference to function template instantiation 'void Sort<FName>(T *,const int32)' being compiled
            with
            [
                T=FName
            ]
    W:\Engine\Engine\Source\Runtime\Core\Public\Containers/Array.h(2519): note: while compiling class template member function 'void TArray<FName,FDefaultAllocator>::Sort(void)'
    W:/NRL/Source/Rugby/GameModes/RugbyGameSession.cpp(256): note: see reference to function template instantiation 'void TArray<FName,FDefaultAllocator>::Sort(void)' being compiled
    W:\Engine\Engine\Source\Runtime\Core\Public\Misc/ITransaction.h(133): note: see reference to class template instantiation 'TArray<FName,FDefaultAllocator>' being compiled
    [18/28] Module.Rugby.26_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/RugbyUnion/PhaseHandlers/RUGamePhaseRuck.cpp(2739): warning C4946: reinterpret_cast used between related classes: 'SSTeam' and 'RUTeam'
    W:\NRL\Source\Rugby\Match/RugbyUnion/RULineoutRater.h(23): note: see declaration of 'SSTeam'
    W:\NRL\Source\Rugby\Match/SSReplaysMk2/SSReplayManager.h(34): note: see declaration of 'RUTeam'
    [19/28] Module.Rugby.1_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Animation/Graph/RugbyAnimNode_PlaySelected.cpp(113): warning C4996: 'FAnimInstanceProxy::CreateUninitializedTickRecord': Please use the overload that takes a group FName Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Animation/Graph/RugbyAnimNode_PlaySelected.cpp(118): warning C4996: 'FAnimInstanceProxy::CreateUninitializedTickRecord': Please use the overload that takes a group FName Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Animation/Graph/RugbyAnimNode_PlaySelected.cpp(164): warning C4996: 'FAnimationRuntime::BlendPosesTogether': Use BlendPosesTogether with other signature Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [20/28] Module.Rugby.45_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenCustomisePlayerDebug.cpp(919): warning C4996: 'getenv': This function or variable may be unsafe. Consider using _dupenv_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenCustomiseSelectKit.cpp(415): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenCustomiseSelectTeam.cpp(1023): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenFulltimeTeamStatsWindow.cpp(106): warning C4305: 'argument': truncation from 'double' to 'float'
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenFulltimeWindow.cpp(47): warning C4305: 'argument': truncation from 'double' to 'float'
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenGraphicsSettings.cpp(60): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenGraphicsSettings.cpp(172): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenInGameInjury.cpp(215): warning C4996: UObjectProperty has been renamed to FObjectProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenInGameInjury.cpp(215): warning C4996: UObjectProperty has been renamed to FObjectProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenInGameInjury.cpp(215): warning C4996: 'FindField': FindField will no longer return properties. Use FindFProperty instead or FindUField if you want to find functions or enums. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenInGameInjury.cpp(252): warning C4996: 'FSlateApplication::FindWidgetWindow': The FindWidgetWindow method that takes an FWidgetPath has been deprecated.  If you dont need the widget path, use FindWidgetWindow(MyWidget) instead.  If you need the path use GeneratePathToWidget Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenListPlayers.cpp(1778): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/UI/Screens/WWUIScreenListTeams.cpp(1960): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [21/28] Module.Rugby.27_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/RugbyUnion/RUDBTeam.cpp(2606): warning C4458: declaration of 'db_id' hides class member
    W:\NRL\Source\Rugby\Databases/SqliteMabObject.h(44): note: see declaration of 'SqliteMabObject::db_id'
    [22/28] Module.Rugby.48_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\ThirdParty\Steamworks\Steamv146\sdk\public\steam\matchmakingtypes.h(45): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:\Engine\Engine\Source\ThirdParty\Steamworks\Steamv146\sdk\public\steam\matchmakingtypes.h(47): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:\Engine\Engine\Source\ThirdParty\Steamworks\Steamv146\sdk\public\steam\matchmakingtypes.h(161): warning C4996: '_snprintf': This function or variable may be unsafe. Consider using _snprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:\Engine\Engine\Source\ThirdParty\Steamworks\Steamv146\sdk\public\steam\matchmakingtypes.h(246): warning C4996: 'strncpy': This function or variable may be unsafe. Consider using strncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
W:\NRL\Source\Rugby\UI\WWUISteamOverlayTracker.h(17): warning C4355: 'this': used in base member initializer list
    [23/28] Module.Rugby.34_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Editor/PropertyEditor/Public/DetailWidgetRow.h(98): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Editor/PropertyEditor/Public/DetailWidgetRow.h(99): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Editor/PropertyEditor/Public/DetailWidgetRow.h(100): warning C4355: 'this': used in base member initializer list
W:\NRL\Plugins\wwStadiumTool\Source\wwStadiumRuntime/Public/SkyDomeBase.h(466): warning C4996: UProperty has been renamed to FProperty Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/SIFGameWorld.cpp(998): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/SIFGameWorld.cpp(1978): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/SIFGameWorld.cpp(3786): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/SIFGameWorld.cpp(5211): warning C4996: 'APlayerState::PlayerId': This member will be made private. Use GetPlayerId or SetPlayerId instead. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/SIFGameWorld.cpp(6766): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/SIFGameWorld.cpp(6803): warning C4996: 'AActor::GetComponentsByClass': Use one of the GetComponents implementations as appropriate Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [24/28] Module.Rugby.15_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/Camera/SSCameraDirector.cpp(325): warning C4305: 'initializing': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
    [25/28] Module.Rugby.19_of_50.cpp
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(378): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Engine\Classes\Kismet/KismetSystemLibrary.h(392): warning C4305: 'argument': truncation from 'double' to 'float'
W:\Engine\Engine\Source\Runtime\Core\Public\Serialization/BufferArchive.h(18): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBarTrack.h(44): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Slate\Public\Widgets/Layout/SScrollBox.h(445): warning C4355: 'this': used in base member initializer list
W:\Engine\Engine\Source\Runtime\Engine\Classes\Animation/AnimNodeBase.h(569): warning C4355: 'this': used in base member initializer list
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(872): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(899): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(1000): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(1387): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(1393): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(1802): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(3541): warning C4996: 'FString::Printf': The formatting string must now be a TCHAR string literal. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(4106): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(4109): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(4118): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/HUD/RUHUDUpdater.cpp(4133): warning C4996: 'UInvalidationBox::InvalidateCache': InvalidationCache is not used. Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
W:/NRL/Source/Rugby/Match/Input/SSInputManager.cpp(205): warning C4996: 'UEnum::GetEnumName': GetEnumName is deprecated, call GetNameStringByIndex instead Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile.
    [26/28] UE4Editor-Rugby.lib
       Creating library W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.lib and object W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.exp
    [27/28] UE4Editor-Rugby.dll
       Creating library W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.suppressed.lib and object W:\NRL\Intermediate\Build\Win64\UE4Editor\Development\Rugby\UE4Editor-Rugby.suppressed.exp
    [28/28] RugbyEditor.target
  Total time in Parallel executor: 151.21 seconds
  Total execution time: 152.51 seconds
