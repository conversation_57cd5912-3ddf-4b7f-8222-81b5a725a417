/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef RU_GAMEPHASEPLAYTHEBALL_H
#define RU_GAMEPHASEPLAYTHEBALL_H

#include "Match/RugbyUnion/RUGameState.h"

class RUGamePhasePlayTheBall : public RUGamePhaseHandler
{
	MABRUNTIMETYPE_HEADER(RUGamePhasePlayTheBall);
public:
	RUGamePhasePlayTheBall(SIFGameWorld *ggame);
	virtual ~RUGamePhasePlayTheBall();

	virtual void Enter();
	virtual void Exit();
	virtual void UpdateSimulation( const MabTimeStep& game_time_step );
	virtual void Reset();

	enum class EPreloadPassDirection
	{
		LEFT = -1,
		RIGHT = 1,
		NONE = 0
	};

	inline void PreloadPass(EPreloadPassDirection preload_direction) { has_preloaded_pass = true; preload_pass_direction = (int)preload_direction; }
	inline bool IsPassPreloaded() const { return has_preloaded_pass; }
	inline int GetPreloadPassDirection() const { return preload_pass_direction; }
	inline void SetBestPreloadReceiver(ARugbyCharacter* receiver) { preload_player_receiver = receiver; }

	float GetEstimatedTimeTillBallBackInPlay();

private:
	void PerformPreloadedPass();

private:
	SIFGameWorld	*game;

	bool is_handover = false;
	bool has_preloaded_pass = false;
	int preload_pass_direction = 0;
	ARugbyCharacter* preload_player_receiver = nullptr;
};

#endif //RU_GAMEPHASEPLAYTHEBALL_H
