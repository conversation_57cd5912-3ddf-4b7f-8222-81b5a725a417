/*--------------------------------------------------------------
|        Copyright (C) 1997-2007 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef SS_EVDS_FORMATIONMANAGER_H
#define SS_EVDS_FORMATIONMANAGER_H

#include <array>

#include "Mab/AdvMath/MabNURBSSpline.h"
#include "Mab/Central/MabEventDataSystem.h"
#include "Mab/Mem/MabMemLib.h"
#include "Mab/Time/MabTimer.h"
#include "Mab/Types/MabArray.h"
#include "Match/AI/RUPassPriority.h"
#include "Match/RugbyUnion/Enums/RUPlayerMovementEnum.h"
#include "Match/RugbyUnion/Enums/RUPlayerPositionEnum.h"
#include "Match/RugbyUnion/RUGameState.h"
#include "Match/SIFObjectLists.h"

#include "RULimits.h"

#include "FormationsManager.h"

class SSRole;
class SSTeam;
class RUDebugService;
class SSRoleArea;
class SIFDebugDrawPool;
class IRUOrigin;
class SIFAsyncLoadingThread;
class SSSetPlayManager;

#define NUM_FORMATION_PLAYERS	(NUM_PLAYERS_PER_TEAM+1)

const int FORMATION_NAME_LEN = 32;
const int FMAREA_NAME_LEN = 32;
const int FMAREA_MAX_SLOTS = 16;

const int OANP_NO_OVERRIDE = -1;
const int OANP_ALL_ROLES = -1;
const float OALS_NO_OVERRIDE = -1.0f;

const int OVERRIDE_FITNESS = 9999;
const int UNSELECTABLE_FITNESS = -9999;
const int MINIMUM_FITNESS = -1000;

//-------------------------------------------------------------------------
// SSEVDSFormationManager
//
// The formation manager is responsible for maintaining all high level
// team/player positioning state through a data driven EVDS format.
//
// High level concepts:
//  * Formation - all state encapsulating:
//      player positioning
//      logical areas for groups of players
//      roles to be assigned to players within roles
//  * Area - a logical grouping of players with a particular purpose.  Types include:
//		Line - used for backlines primarily
//      Elliptical - a standard elliptical shape (data driven) in which players can be distributed
//
// Each team has one formation manager assigned to it
//   * Formations are selected based on a scoring system
//     which is updated every frame
//
// <AUTHOR> Harrison
//
// Refactored/commented by Tyrone McAuley (with thanks from Rob)
//-------------------------------------------------------------------------

//#rc3_legacy class SSEVDSFormationEditorInterface;

class SSEVDSFormationManager //#rc3_legacy : public MabEVDSEditor
{
	friend class SSRoleArea;
public:
	/// Con/Destruction
	SSEVDSFormationManager(SIFGameWorld *ggame, SSTeam *tteam);
	virtual ~SSEVDSFormationManager();

	/// Update functions
	void			UpdateLogic(float delta_game_time);

	void			UpdateLogicPostPlayerUpdate();
	void			UpdateLogicPostPlayerUpdate2();

	/// Force the given formation for this team
	void ForceFormation(const FString& name, int horizontalDirection);

	/// Force clear the formation for this team
	void ForceClearFormation();

	/// Disable formation changes during current phase
	void			DisableFormationChangeDuringPhase();

	/// Enable formation changes during current phase
	void			EnableFormationChangeDuringPhase();

	/// Reset the formation manager
	void			Reset();
	void			GameReset();
	void			printState();

	#ifdef ENABLE_RUGED
	void			SendDebugInfo(RUDebugService *service);
	#endif

	/// Return the origin of the current formation - off which all player positions are relative
	FVector		GetOrigin();
	const IRUOrigin* GetFormationOrigin() const { return formation_origin; }
	const FString& GetCurrentFormationName();

	/// Get the team that this formation is for
	SSTeam* GetTeam() { return team; }
	const SSTeam* GetTeam() const { return team; }

	inline int GetCurrentFormationXDirection() const { return current_x_direction; }

	/// Player has been deleted, remove any references.
	void			RemovePlayer(ARugbyCharacter* player);
	/// Player has been swapped (substituted), replace any references.
	void			SwapPlayer(ARugbyCharacter* player, ARugbyCharacter* replacement);

	/// Has async formation load finished (or not been started!)
	//#rc3_legacy inline bool		IsAsyncJobFinished(){ return !queued_async_job; }

	//Temporary static function to stop AI. Can be removed when debug commands are implemented - #MB 
	static void SetAIRunning(bool isRunning);

	template <typename T> FDataTableRowHandle AddOrFindRow(UDataTable * inDatatable, T row);

	///-----------------------------------------
	/// Movement methods
	///-----------------------------------------

	/// Get standard formation movement position and urgency
	void	GetStandardFormationMovement( ARugbyCharacter* player, FVector& returned_position, float& returned_urgency, ACTOR_SPEED& max_actor_speed );
	void	DoStandardFormationMovement( ARugbyCharacter* player, bool force  = false);
	float	GetWaypointTravelledFraction(ARugbyCharacter * player);
	void	DoRefereeMovement(ARugbyCharacter* player);
	void	MovePlayersToKickOffPositions();
	int		UpdatePlayerSlot( ARugbyCharacter* player, bool apply_standard_exclusions = true );

	/// Get a players urgency by participation
	float	GetUrgencyByParticipation( ARugbyCharacter* player, ACTOR_SPEED& returned_max_actor_speed );

	void	HandleFifthTackleWingerRepositioning();
	bool	ShouldRepositionWingersForFifthTackle() const;

	/// Needed to format mirrored formations like lineouts
	void	ForceUpdateCurrentFormation();

	/// Request the async loading of all formations.
	//#rc3_legacy void			StartAsyncLoadingOfFormations();

	void	StartLoadingOfFormations();

	void	ClampPlayerZPosition( ARugbyCharacter*  player, FVector &position );

	///-----------------------------------------------------
	/// Helpers
	///-----------------------------------------------------
	SSRoleArea*	GetPlayerArea(ARugbyCharacter* player);

	static MabTypeID	GetRoleFromEnum(ERugbyFormationRole role);
	ARugbyCharacter*	GetPlayerToMark(ARugbyCharacter* me);
	void				SetWarpOnCutSceneFinish();
	SSSetPlayManager *	GetSetplayManager() { return SetplayManager; }
	ARugbyCharacter*	GetSetplayFirstReceiver();

	/// \brief OverrideAreaNumPlayers
	/// @param area_name The name of the area for which to apply the override accepts trailing wildcard of * e.g Backs* will match all areas that start with Backs
	/// @param num_players The number of players to override for this region.  Set to OANP_NO_OVERRIDE to revert to initial formation setting
	/// @param role_id The sub role id to apply this to.  Set to OANP_ALL_ROLES to apply this setting to all roles for the formation.  Note an assert will fire if this is the formation contains more than one role entry
	/// @param formation_name The formation to apply the role override to.  NULL uses the currently active formation

	bool OverrideAreaNumPlayers(const FString& areaName, int numPlayers, ERugbyFormationRole role, const FString& formationName = {});

	void OverrideAreaLineSpacing(const FString& areaName, const float lineSpacing, const FString& formationName = {});

	/// Reset num player overrides for ALL formations.
	void ResetNumPlayersOverrides();

	///-----------------------------------------------------
	/// EVDS live editing interface
	///-----------------------------------------------------

	virtual void EVDSStopEditing(const MabString &file_name);
	virtual void EVDSStartEditing(const MabString &file_name);
	virtual void EVDSDataModified(const MabString &file_name);
	virtual void EVDSSetTime(const MabString &file_name, float time);
	virtual void EVDSGenericCommand(const MabString &arguments);

	///-----------------------------------------------------
	/// Pass priority interface
	///-----------------------------------------------------

	void		UpdatePassPriorities( int pass_priorities_to_apply = ~0, bool force_update = false ); // Apply everything by default
	void		ResetPassPriorityTimer( bool initial_event = false );

	///-----------------------------------
	void ForceUpdateRoles()							{ UpdateRoles(); }
	FSerialiseFormation* GetFormationByName(const FString& name);

private:
	//Temporary static variable for controlling AI, can be removed when debug controls added #MB 
	static bool AIDisabledForCutscenes;

	/// Sub methods of Standard formation movement
	float			GetPlayerUrgency(SSRoleArea* area, ARugbyCharacter* player, int player_no, FVector& position, ACTOR_SPEED& max_actor_speed);
	void			GetEllipticalZoneMovement(SSRoleArea* area, FVector& position, float play_dir, ARugbyCharacter* player, int slot_idx, float time = 0.0f);
	void			GetLineZoneMovement(SSRoleArea* area, float play_dir, int slot_idx, FVector& position, ARugbyCharacter* player, int player_no);
	using EVDSFormation = FSerialiseFormation;
	/*
	class EVDSFormation
	{
	public:
		/// Con/destruction
		EVDSFormation();

		MabString	file_name;				/// The filename for the container  (not including path)

		MabEVDSEventHandle	fm_event;		/// The EVDS event that this formation is based on
		char	name[FORMATION_NAME_LEN];	/// The name of this formation

		// Control parameters/Conditions
		int		strategy;
		int		origin_target;				/// What origin should be used for this formation?

		int		attacking;					/// Is this an attacking formation?
		int		game_phase;					/// What game phase should this formation run in?
		float	xmin,xmax;					/// X bounds for formation origin
		float	zmin,zmax;					/// Z bounds for formation origin

		bool	x_center;					/// Should the formation origin be locked to x=0.0?
		bool	z_center;					/// Should the formation origin be locked to z=0.0?
		bool	allow_x_mirror;				/// Can this formation be mirroed in the x?
		bool	warp_to_positions;

		int		default_role_id;			/// The default role id to be used for this formation
		int		default_idle_group;			/// The default idle animation group to be used for this formation (controls idle animations)

		// Data.
		MabEVDSEventHandles zones;			/// All of the zones associated with this formation
		MabEVDSEventHandles lines;			/// All of the lines associated with this formation

//		TArray <FFormationZoneInfo> gFormationZoneInfo; //should be moved to the header.
//		TArray <FFormationLinesInfo> gFormationLineInfo;
//		FRugbyFormation gRugbyFormation;
	};*/

	/// Load a formation.
	//#rc3_legacy static void	LoadFormation(const MabString& fname, MabEVDS* evds, std::vector<std::shared_ptr<EVDSFormation>>& formations );
	/// Async load job to load up all formations in background.
	//#rc3_legacy static void		LoadFormationsAsyncJob(void *user_data, SIFAsyncLoadingThread *load_thread, bool is_abort);

	void LoadFormationsFromDatatable(); // Called to setup/update 'formations'

	void SetupCurrentFormationData(); // Called when formation has changed - sets up all areas, zones etc...

	void SetupCurrentSetplayData();

	std::vector<SSRoleArea *> current_areas;
	std::vector<SSRoleArea *> current_setplay_areas;
	SSRoleArea* GetAreaByName(const FString& name);

	// For role assignment.
	struct FMRole
	{
		MabTypeID role = MABTYPE_UNKNOWN;
		ERugbyFormationPlayerMask player_mask = ERugbyFormationPlayerMask::ALL;
		SSRoleArea* area = nullptr;

		bool operator==(const FMRole &other) const
		{
			return (role==other.role) && (player_mask==other.player_mask) && (area==other.area);
		}
		bool operator!=(const FMRole &other) const
		{
			return !(*this==other);
		}
	};

	///-----------------------------------

private:
	const SSEVDSFormationManager* GetOppositionFormationManager() const;
	const SSRoleArea* GetLineWithDirection(int direction) const;
	const SSRoleArea* GetOpposingLine(const SSRoleArea* line) const;

	bool			ShouldUpdatePassPriorities();

	///-------------------------------------------------------
	/// Role assignment
	///-------------------------------------------------------
	void			UpdateRoles();
	void			AssignPlayerRoles(MabVector<FMRole> &roles, const SIFRugbyCharacterList& players, MabTypeID default_role );
	int				GetBestPlayerAssignment(const SIFRugbyCharacterList& players, const MabVector<FMRole>& roles, int role_index);

	void			SetPlayerRole(ARugbyCharacter* player, SSRole* new_role, SSRoleArea* area);
	int				PlayerMaskFitnessAlteration(PLAYER_POSITION position, ERugbyFormationPlayerMask playerMask, const SSRoleArea* area);

	/// Update the current formation - rescoring all candidate formations
	bool			UpdateCurrentFormation();
	float			GetFormationScore(EVDSFormation *formation,FVector &pos, bool is_attacking,int direction);

	bool			SetCurrentFormation(EVDSFormation *best_formation, int best_direction);

	/// Update the current formation origin
	void			UpdateFormationOrigin();

	// Get a receiver if we are in play the ball phase
	ARugbyCharacter* GetReceivingPlayer();

	///-----------------------------------

	SIFGameWorld* game = nullptr;
	SSTeam* team = nullptr;
	SSSetPlayManager * SetplayManager = nullptr;

	std::vector<std::unique_ptr<FSerialiseFormation>> mFormations;
	FSerialiseFormation* mCurrentFormation = nullptr;
	FSerialiseFormation* mCurrentSetplay = nullptr;

	bool			disable_formation;
	bool			disable_formation_change;
	bool			disable_formation_change_during_phase;
	RUGamePhase	game_phase_at_formation_change;

	//#rc3_legacy MABMEM_HEAP		heap;
	//#rc3_legacy bool			queued_async_job;

	//-----------------------------------

	struct FormationPlayer
	{
		SSRoleArea* current_area = nullptr;
		float initial_urgency = 0.7f;
	};

	std::array<FormationPlayer, NUM_FORMATION_PLAYERS_INIT> mFormationPlayers;


	//-----------------------------------

	int				current_x_direction;

	//#rc3_legacy int				current_strategy;				/// ???

	const IRUOrigin*	formation_origin;
	float			formation_start_x;				/// The x position of the formation when it was first set

	bool			formation_changed_this_frame;

	bool			warp_next_formation;
	bool			warp_completed = false;
	TMap<ARugbyCharacter *, bool> playersWarped;

	int				backline_players_left;
	int				backline_players_right;
	int				backline_players_total;

	bool			force_position_evaluation;		// Set when formation changes to ensure we evaluate positions.

	int hitup_runners = 0;
	int hitup_supports = 0;
	int hitup_decoys = 0;

	// From SSStrategy

	MabTimer		pass_priority_timer;

	std::vector<float> formation_scores;
	int role_table[NUM_PLAYERS_INIT * NUM_PLAYERS_PER_TEAM_INIT];

	// WJS RLC Not Needed ERugbyFormationPlayerMask ConvertFifteensPositionToSevens(ERugbyFormationPlayerMask inPos);

#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	void UpdateDebug();

#endif
};



///-------------------------------------------------------------------------
/// SSRoleArea...
///-------------------------------------------------------------------------

class SSRoleArea
{
	friend class SSEVDSFormationManager;

public:

	SSRoleArea(const SIFGameWorld& _game, const SSEVDSFormationManager& _manager);

	void GetXRange(float& start, float& end) const;

	bool is_line() const { return isLine; }

	float get_priority() const { return isLine ? line->priority : zone->priority; }
	const FString& get_name() const { return isLine ? line->name : zone->name; }
	ERugbyFormationPlayerMask get_player_mask() const { return isLine ? line->playerMask : zone->playerMask; }
	FString getPlayerMaskAsString();
	ERugbyFormationTarget get_origin_target() const { return isLine ? line->originTarget : zone->originTarget; }
	ERugbyFormationGroupStrategyType get_group_strategy() const { return isLine ? line->groupStrategy : zone->groupStrategy; }
	ERugbyFormationUrgencyMode get_urgency_mode() const { return isLine ? line->urgencyMode : zone->urgencyMode; }
	float get_break_tackle_boost() const { return isLine ? line->breakTackleBoost : zone->breakTackleBoost; }
	float get_min_urgency() const { return isLine ? line->minUrgency : zone->minUrgency; }
	float get_max_urgency() const { return isLine ? line->maxUrgency : zone->maxUrgency; }
	float get_min_urgency_dist_x() const { return isLine ? line->minUrgencyDistX : zone->minUrgencyDistX; }
	float get_max_urgency_dist_x() const { return isLine ? line->maxUrgencyDistX : zone->maxUrgencyDistX; }
	TArray<FSerialiseFormationRole>& get_roles() { return isLine ? line->roles : zone->roles; }
	FSerialiseFormationZone* get_zone() { return zone; }

	FVector			GetPathPosition(float t, bool world_space) const;
	float			GetPathLength() const;

	int				GetPlayerSlotIdx(ARugbyCharacter* player);

	inline			int SlotsUsed() const { return (int)(slots.size()); }

	ARugbyCharacter* GetSlotPlayer(int slot_idx) const;

	float			GetPathZOffset(ARugbyCharacter* player);

	float			GetLineIntersectT(FVector pos, float angle);

	bool			GetPlayers(SIFRugbyCharacterList& results);

	/// This must be called every frame to ensure that this player remains part of the slot setup
	/// Updates the slot for the current player
	int				UpdatePlayerSlot( ARugbyCharacter* player );

	const IRUOrigin* GetOrigin() { return origin; }
	FVector			GetZonePosition(float play_dir, float time = 0.0f) const;

	/// Player has been deleted, remove any references.
	void			RemovePlayer(ARugbyCharacter* player);
	/// Player has been swapped (substituted), replace any references.
	void			SwapPlayer(ARugbyCharacter* player, ARugbyCharacter* replacement);

	enum OPENSIDE_DIR { OSD_LEFT, OSD_RIGHT };
	enum OPENSIDE_SIZE { OSS_BIG, OSS_SMALL };

	OPENSIDE_DIR GetOpenside() { return openside_dir; };

	void			GetPlayerZonePosition(ARugbyCharacter* player, int slot_idx, float &dx, float &dz, float time = 0.0f);
private:


	void			Update();

	void UpdateZoneOpensideBias();

	void			UpdateUnusedSlots();
	void			Reset();
	void			UpdateSpline();

	

	/// Each player in a position within the area is in a slot
	struct SlotEntry
	{
		SlotEntry( ARugbyCharacter* player ) : player( player ), used_last_frame( true ) {}
		ARugbyCharacter* player;
		bool used_last_frame;
	};

	using SlotEntries = std::vector< SlotEntry >;
	SlotEntries slots;			/// Current slots
	SlotEntries slots_open;		/// Open side slots
	SlotEntries slots_blind;	/// Blind side slots
	FVector slot_reference;	/// Reference point for slot calculations

	/// Openside/Blindside info
	
	OPENSIDE_DIR openside_dir;
	OPENSIDE_SIZE openside_size;

	void UpdateOpenBlind();
	void SelectPlayer( PLAYER_POSITION pos, SlotEntries& to_choose_from, SlotEntries& add_to );
	bool PickNextPlayer( SSRoleArea::SlotEntries& to_choose_from, SSRoleArea::SlotEntries& add_to, const PLAYER_POSITION* positions, const int n_positions ) const;

	friend struct PlayerSlotMatch;
	friend struct SlotUsedMatch;
	friend struct LineTSort;
	int slot_count_last_frame;


	float			slot_x_offsets[NUM_PLAYERS_PER_TEAM_INIT];
	float			slot_z_offsets[NUM_PLAYERS_PER_TEAM_INIT];

	int				players_in_area;								// Bitfields for all players 'position' or'ed into here.

	const SIFGameWorld* game = nullptr;
	const SSEVDSFormationManager* manager = nullptr;

	bool isLine = false; // Is this area a line type?
	bool doReset = false;

	// these could be in a yucky c union, but I doubt that's worth the effort
	FSerialiseFormationZone* zone = nullptr;
	FSerialiseFormationLine* line = nullptr;

	MabNURBSSpline spline;
	float splineLength = 0.f;

	// Mark Defence info
	std::array<int, NUM_PLAYERS_PER_TEAM_INIT> what_att_slot_to_mark;

	void UpdateAreaOrigin();
	const IRUOrigin* origin = nullptr;

	bool bIsFlipped = false;
};

#endif // SS_FORMATIONMANAGER_H
