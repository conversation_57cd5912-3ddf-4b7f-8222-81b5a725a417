/*--------------------------------------------------------------
|        Copyright (C) 1997-2009 by Prodigy Design Ltd         |
|                     Sidhe Interactive (TM)                   |
|                      All rights Reserved                     |
|--------------------------------------------------------------|
|                                                              |
|  Unauthorised use or distribution of this file or any        |
|  portion of it may result in severe civil and criminal       |
|  Penalties and will be prosecuted to the full extent of the  |
|  law.                                                        |
|                                                              |
---------------------------------------------------------------*/

#ifndef _SSInputManager_H_
#define _SSInputManager_H_

#include "Match/RugbyUnion/Enums/RULineoutEnum.h"
#include "Mab/MabInclude.h"
#include "RUPassSetplayInterface.h"

class SSHumanPlayer;
class RUTeam;
class SSCameraManager;

class SIFGameWorld;
class ARugbyCharacter;
class SIFWiiControllerHelper;
class RU3DHUDManager;
class RUInputKickInterface;
class RUInputPhaseDecision;
class RUPassExtendInterface;
class MabNetMessageDatabase;
class MabTimeStep;
class MabControlActionManager;

/**
// Dispatch input for Rugby League 3.
//
// <AUTHOR> Baker.
*/
class SSInputManager
{
public:
	SSInputManager( MabControlActionManager* ccontrol_action_manager, SIFGameWorld* ggame, RU3DHUDManager* hud_manager );
	~SSInputManager();

	void UpdateSimulation( const MabTimeStep& delta_time_step );
	void Reset();
	void GameReset();

	/// Sets amount of time before input values are updated
	/// This is basically a hack to get around the issue where the pause menu buttons affect the game
	//void SetUpdateDelay( float time );

	RUInputKickInterface* GetKickInterface() { return kick_interface;	}

	RUInputPhaseDecision* GetDecisionInterface() { return decision_interface; }

	RUPassExtendInterface* GetPassExtendInterface() { return pass_extend_interface; }

	/// Debounce all inputs - call this when re-entering from a major break in play
	void DebounceAllActionsForAllPlayers();

//	void SetHudManager( RL3HudManager* hhud_manager );

#if PLATFORM_WII
	bool CanPerformGesture( int controller_index );
	void SetGesturePerformed( int controller_index );
#endif

	/// TEMP : While testing scrums
	MabControlActionManager* GetControlActionManager() const { return control_action_manager; }

private:
	void UpdateInputDelta(SSHumanPlayer* human_player, float delta_time);
#if !(UE_BUILD_SHIPPING || UE_BUILD_TEST)
	bool UpdateDebug(SSHumanPlayer* human_player);
#endif
	void UpdateAggression(SSHumanPlayer* human_player);
	void UpdatePassing( SSHumanPlayer* human_player );
	void UpdateProWithoutBall( SSHumanPlayer* human_player );
	void UpdateTackling( SSHumanPlayer* human_player );
	void UpdateFending( SSHumanPlayer* human_player );
	void UpdateSprinting( SSHumanPlayer* human_player );
	void UpdateMovement( SSHumanPlayer* human_player );
	void UpdateSidestep( SSHumanPlayer* human_player );
	void UpdateSkipCutscene( SSHumanPlayer* human_player );
	bool UpdateTryScore( SSHumanPlayer* human_player );
	bool UpdateSelection( SSHumanPlayer* human_player );
	void UpdateKicking( SSHumanPlayer* human_player );

	bool WasHumanExpectingBall( SSHumanPlayer* human_player ) const;

	void UpdateLooseBall( SSHumanPlayer* human_player );
	void UpdateDefenceLine( SSHumanPlayer* human_player );
	void UpdateCamera( SSHumanPlayer* human_player );
	void UpdateProCameraLockBall( SSHumanPlayer* human_player );
	void UpdateUserStrategySelection( SSHumanPlayer* human_player );
	void UpdateRuckControls( SSHumanPlayer* human_player );
	void UpdateMaulControls( SSHumanPlayer* human_player );
	void UpdateExtraTimeCoinTossControls( SSHumanPlayer* human_player );
	void UpdatePlayTheBallPreloadPass( SSHumanPlayer* human_player );
	void UpdateScrumPreloadPass(SSHumanPlayer* human_player);
	ARugbyCharacter* GetBestReceiver(SSHumanPlayer* human_player, int pass_dir);
	void UpdateScrumControls( SSHumanPlayer* human_player );
	void UpdateDecisionControls( SSHumanPlayer* human_player );
	void UpdateStrategyControls( SSHumanPlayer* human_player );
	
	THROW_TYPE GetLineoutThrowType (SSHumanPlayer* human_player) const;

private:
	MabControlActionManager*		control_action_manager;	///< The MabControlActionManager that input is queried from.
	SIFGameWorld*					game;					///< The SIFGameWorld that this SSInputManager is dispatching input for.
	RUInputKickInterface*			kick_interface;			///< The kick interface
	RUInputPhaseDecision*			decision_interface;
	//RL3UserStrategyHandler*		user_strategy_handler;
	SIFWiiControllerHelper*			controller_helper;		///< The controller helper
	RUPassExtendInterface*			pass_extend_interface;	/// The interface used for passing more than one player out
	//RL3HudManager*				hud_manager;
	float							begin_update_time;		///< Point at which we should start updating controllers
#if PLATFORM_WII
	bool						dummy_passing[4];			/// stores whether we were dummy passing last frame
	bool						sidestepping[4];			/// stores whether we were sidestepping last frame
	bool						fending[4];					/// stores whether we were fending last frame
	float						gesture_timers[4];			/// stores the time until we're allowed to process another gesture
#endif
};

#endif // #ifndef _SSInputManager_H_

